import React, { useState } from 'react'
import { MdPlace } from "react-icons/md";
import FibonacciGallery from './FibonacciGallery';
import { FaRegHeart } from "react-icons/fa";
import { FaRegComment } from "react-icons/fa";
import CommentSection from './Comment';
import { useDispatch, useSelector } from 'react-redux';
import { toggleSavePost } from '../Reducer/Actions';
import { IoBookmarkOutline, IoBookmark, IoTrendingUp } from "react-icons/io5";
import { useNavigate } from 'react-router-dom';
import Image from './image';

function Post(props) {
    const navigate = useNavigate();
    const [commentOpen, setCommentOpen] = useState(false);
    const dispatch = useDispatch();
    const savedPosts = useSelector(state => state.savedPosts);
    const trendingTags = useSelector(state => state.trendingTags);
    const isSaved = savedPosts.includes(props.post.id);

    // Helper function to check if a tag is trending
    const isTagTrending = (tagName) => {
        return trendingTags.data.some(trendingTag =>
            (trendingTag.tag || trendingTag.title) === tagName
        );
    };

    const handleSave = (e) => {
        e.preventDefault();
        e.stopPropagation();
        dispatch(toggleSavePost(props.post.id));
    };

    const handleClick = () => {
        navigate(`/post/${props.post.id}`);
    };

    return (
        <div 
            className='bg-white rounded-3xl shadow-lg p-4 m-4 cursor-pointer hover:shadow-xl transition-shadow duration-200'
            onClick={handleClick}
        >
            <div className='mx-3 font-bold flex items-start justify-start gap-3'>
                <Image src={props.post.user?.profil} className='w-10 h-10 rounded-full object-cover' alt="" />
                <div className="flex flex-col items-start justify-start">
                    {props.post.user?.nom}
                    <span className='text-gray-500 font-normal text-xs'>@ {props.post.user?.username}</span>
                </div>
                <a 
                    href={props.post.map_url} 
                    className='ml-auto no-underline flex items-center bg-gray px-2 py-1 rounded-full justify-center gap-2 text-gray-500 text-sm'
                >
                    <MdPlace />{props.post.location}
                </a>
                <button
                    onClick={handleSave}
                    className={`p-2 rounded-full transition-colors duration-200 ${
                        isSaved ? 'text-secondary' : 'text-gray-500 hover:text-secondary'
                    }`}
                >
                    {isSaved ? <IoBookmark className="w-5 h-5" /> : <IoBookmarkOutline className="w-5 h-5" />}
                </button>
            </div>
            <p className='m-3'>
                {props.post.description}
            </p>
            {props.post.images.length > 0 && 
            <FibonacciGallery images={props.post.images} />
            }
            <div className='my-3 font-bold flex items-start justify-start gap-3'>
                {props.post.tags?.map((hashtag) => {
                    const isTrending = isTagTrending(hashtag.tag);
                    return (
                        <span
                            key={hashtag.id}
                            className={`p-1 px-2 rounded font-normal text-xs flex items-center gap-1 ${
                                isTrending
                                    ? 'text-secondary bg-secondary/10 border border-secondary/20'
                                    : 'text-gray-500 bg-gray'
                            }`}
                        >
                            #{hashtag.tag}
                            {isTrending && (
                                <IoTrendingUp className="w-3 h-3" />
                            )}
                        </span>
                    );
                })}
            </div>
            <div className='mx-3 font-bold flex items-start justify-start gap-4'>
                <span className='text-gray-500 font-normal flex items-center justify-center gap-2 cursor-pointer text-base'>
                    <FaRegHeart className="w-5 h-5" /> {props.post?.likes || 0}
                </span>
                <span 
                    onClick={() => setCommentOpen(!commentOpen)} 
                    className='text-gray-500 font-normal flex cursor-pointer items-center justify-center gap-2 text-base'
                >
                    <FaRegComment className="w-5 h-5" />
                </span>
               
            </div>
            
            {commentOpen && <CommentSection comments={props.post.comments || 0} />}
        </div>
    )
}
export default Post
