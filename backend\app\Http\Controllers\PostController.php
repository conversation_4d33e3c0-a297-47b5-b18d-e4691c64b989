<?php

namespace App\Http\Controllers;

use App\Models\Post;
use App\Models\Tag;
use App\Models\Image;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Log;

class PostController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $posts = Post::with(['tags', 'images' , 'user'])->get();
        return response()->json($posts);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $tags = Tag::all();
        return response()->json($tags);
    }
    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'description' => 'required',
            'location' => 'nullable|string',
            'tags' => 'array',
            'tags.*' => 'string',
            'images.*' => 'nullable|image'
        ]);

        // Debug authentication
        Log::info('Auth check: ' . (Auth::check() ? 'true' : 'false'));
        Log::info('Auth ID: ' . Auth::id());
        Log::info('Auth user: ', Auth::user() ? Auth::user()->toArray() : ['No user']);
        
        $post = new Post();
        $post->description = $request->description;
        $post->location = $request->location;
        
        // Try different methods to get user_id
        $post->user_id = Auth::id() ?? $request->user()->id ?? null;
        
        // Log the final user_id
        Log::info('Final user_id set: ' . $post->user_id);
        
        // Auto-generate map URL if location is provided
        if ($request->localisation) {
            $post->map_url = 'https://www.google.com/maps/search/?api=1&query=' . urlencode($request->localisation);
        }
        
        $post->save();

        // Créer les tags s'ils n'existent pas, puis les attacher
        if ($request->has('tags')) {
            $tagIds = [];

            foreach ($request->tags as $tagName) {
                $tag = Tag::firstOrCreate(['tag' => $tagName]);
                $tagIds[] = $tag->id;
            }

            $post->tags()->sync($tagIds);
        }

        // Save multiple images
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $img) {
                $path = $img->store('posts/images', 'public');
                $post->images()->create(['image' => $path]);
            }
        }

        return response()->json(['message' => 'Post created successfully', 'post' => $post]);
    }


    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $post = Post::with(['tags', 'images', 'user'])->findOrFail($id);
        return response()->json($post);
    }
    /**
     * Show the form for editing the specified resource.
     */
   public function edit($id)
{
    $post = Post::with(['tags', 'images'])->findOrFail($id);
    $allTags = Tag::all();

    return response()->json([
        'post' => $post,
        'all_tags' => $allTags
    ]);
}
    /**
     * Update the specified resource in storage.
     */
     public function Update(Request $request, $id)
{
    $post = Post::findOrFail($id);
    $request->validate([
        'description' => 'sometimes|required|string',
        'localisation' => 'sometimes|nullable|string',
        'tags' => 'sometimes|array',
        'tags.*' => 'string',
        'images.*' => 'sometimes|image',
        'keep_media_ids' => 'sometimes|array',
        'keep_media_ids.*' => 'integer|exists:images,id'
    ]);
    if ($request->has('description')) {
        $post->description = $request->description;
    }
    if ($request->has('localisation')) {
        $post->localisation = $request->localisation;
    }
    $post->save();
    if ($request->has('tags')) {
        $tagIds = [];
        foreach ($request->tags as $tagName) {
            $tag = Tag::firstOrCreate(['tag' => $tagName]);
            $tagIds[] = $tag->id;
        }
        $post->tags()->sync($tagIds);
    }
    if ($request->has('keep_media_ids')) {
        $keepIds = $request->keep_media_ids;
        $imagesToDelete = $post->images()->whereNotIn('id', $keepIds)->get();
        foreach ($imagesToDelete as $image) {
            // حذف من الـ storage (اختياري)
            if (\Storage::disk('public')->exists($image->path)) {
                \Storage::disk('public')->delete($image->path);
            }
            $image->delete();
        }
    }
    if ($request->hasFile('images')) {
        foreach ($request->file('images') as $img) {
            $path = $img->store('posts/images', 'public');
            $post->images()->create(['path' => $path]);
        }
    }

    return response()->json([
        'message' => 'Post updated successfully',
        'post' => $post->load('tags', 'images')
    ]);
}
    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $post = Post::findOrFail($id);
        // حذف الصور المرتبطة
        foreach ($post->images as $image) {
            $image->delete();
        }

        // حذف العلاقة مع التاغات
        $post->tags()->detach();

        $post->delete();

        return response()->json(['message' => 'Post deleted successfully']);
    }
}









