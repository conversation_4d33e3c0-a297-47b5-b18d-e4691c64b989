<?php

namespace App\Http\Controllers;

use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class TagController extends Controller
{
    /**
     * Get all tags with their post counts
     * 
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        try {
            $tags = Tag::getTagsWithPostCount();
            return response()->json($tags);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch tags',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get trending tags
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function trending(Request $request): JsonResponse
    {
        try {
            $limit = $request->get('limit', 10);
            $days = $request->get('days', 7);

            // Use improved trending algorithm if available
            $trendingTags = method_exists(Tag::class, 'getTrendingTagsImproved')
                ? Tag::getTrendingTagsImproved($limit, $days)
                : Tag::getTrendingTags($limit, $days);

            return response()->json([
                'trending_tags' => $trendingTags,
                'meta' => [
                    'limit' => $limit,
                    'days' => $days,
                    'total' => $trendingTags->count()
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch trending tags',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific tag with its posts
     * 
     * @param int $id
     * @return JsonResponse
     */
    public function show($id): JsonResponse
    {
        try {
            $tag = Tag::with(['posts' => function($query) {
                $query->with(['user', 'images', 'tags'])
                      ->orderBy('created_at', 'desc');
            }])->findOrFail($id);
            
            return response()->json([
                'tag' => $tag,
                'posts_count' => $tag->posts->count(),
                'is_trending' => $tag->isTrending()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Tag not found',
                'message' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Search tags by name
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $query = $request->get('q', '');
            $limit = $request->get('limit', 20);
            
            if (empty($query)) {
                return response()->json([
                    'tags' => [],
                    'message' => 'Search query is required'
                ], 400);
            }
            
            $tags = Tag::where('tag', 'LIKE', "%{$query}%")
                       ->withCount('posts')
                       ->orderBy('posts_count', 'desc')
                       ->limit($limit)
                       ->get();
            
            return response()->json([
                'tags' => $tags,
                'query' => $query,
                'total' => $tags->count()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to search tags',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
