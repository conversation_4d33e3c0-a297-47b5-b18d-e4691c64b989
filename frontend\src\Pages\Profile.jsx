import React, { useState } from 'react';
import UserNav from "../Components/UserNav";
import { IoLocationOutline, IoLinkOutline, IoCalendarOutline, IoGridOutline, IoBookmarkOutline } from "react-icons/io5";
import Post from "../Components/Post";
import { useSelector, useDispatch } from 'react-redux';
import { toggleSavePost } from '../Reducer/Actions';
import Image from '../Components/image';

const Profile = () => {
    const [activeTab, setActiveTab] = useState('posts');
    const posts = useSelector((state) => state.posts);
    const user = useSelector((state) => state.user);
    const savedPosts = useSelector((state) => state.savedPosts);
    const dispatch = useDispatch();


    const userStats = {
        posts: posts.filter(post => post.user.id === user.id).length,
        savedPosts: savedPosts.length,
        followers: 1234,
        following: 567,
    };

    const TabButton = ({ id, label, icon: Icon, count }) => (
        <button
            onClick={() => setActiveTab(id)}
            className={`flex items-center gap-2 px-4 py-2 font-medium rounded-xl transition-all duration-200
                ${activeTab === id 
                    ? 'bg-secondary text-white' 
                    : 'text-gray-600 hover:bg-gray-100'}`}
        >
            <Icon className="text-xl" />
            <span>{label}</span>
            {count > 0 && (
                <span className={`text-sm ${activeTab === id ? 'bg-white text-secondary' : 'bg-gray-200'} px-2 py-0.5 rounded-full`}>
                    {count}
                </span>
            )}
        </button>
    );

    const SavedPostCard = ({ post }) => (
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
            <div className="relative group">
                <img 
                    src={post.media[0]} 
                    alt={post.titre}
                    className="w-full h-48 object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center">
                    <button
                        onClick={() => dispatch(toggleSavePost(post.id))}
                        className="opacity-0 group-hover:opacity-100 bg-white text-secondary px-4 py-2 rounded-full font-medium transform translate-y-2 group-hover:translate-y-0 transition-all duration-200"
                    >
                        Unsave
                    </button>
                </div>
            </div>
            <div className="p-4">
                <h3 className="font-semibold text-lg mb-2">{post.titre}</h3>
                <p className="text-gray-600 text-sm line-clamp-2">{post.content}</p>
                <div className="mt-4 flex items-center gap-2">
                    <img 
                        src={post.user.profilePic} 
                        alt={post.user.name}
                        className="w-6 h-6 rounded-full"
                    />
                    <span className="text-sm text-gray-500">{post.user.name}</span>
                </div>
            </div>
        </div>
    );

    return (
        <section className="min-h-screen bg-gray">
            <UserNav />
            
            {/* Profile Header */}
            <div className="relative">
                {/* Cover Photo */}
                <div className="h-64 w-full bg-gradient-to-r from-primary to-secondary">
                    <Image 
                        src={user.cover} 
                        alt="Cover" 
                        className="w-full h-full object-cover"
                    />
                </div>
                
                {/* Profile Info Card */}
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="bg-white rounded-xl shadow-sm -mt-24 p-6">
                        <div className="flex flex-col lg:flex-row items-start lg:items-center gap-6">
                            {/* Profile Picture */}
                            <div className="relative">
                                <Image 
                                    src={user.profil} 
                                    alt={user.name}
                                    className="w-32 h-32 rounded-full border-4 border-white shadow-md object-cover"
                                />
                                <button className="absolute bottom-0 right-0 bg-secondary text-white p-2 rounded-full shadow-lg hover:bg-secondary-dark transition-colors">
                                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                </button>
                            </div>

                            {/* User Info */}
                            <div className="flex-1">
                                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                                    <div>
                                        <h1 className="text-2xl font-bold text-gray-900">{user.name}</h1>
                                        <p className="text-gray-500">@{user.username}</p>
                                    </div>
                                    <button className="px-6 py-2 bg-secondary text-white rounded-xl hover:bg-secondary-dark transition-colors">
                                        Edit Profile
                                    </button>
                                </div>

                                <p className="mt-4 text-gray-600">{user.bio}</p>

                                <div className="mt-4 flex flex-wrap gap-4">
                                    {user.location && (
                                        <div className="flex items-center gap-1 text-gray-500">
                                            <IoLocationOutline />
                                            <span>{user.location}</span>
                                        </div>
                                    )}
                                    {user.website && (
                                        <div className="flex items-center gap-1 text-secondary">
                                            <IoLinkOutline />
                                            <a href={user.website} target="_blank" rel="noopener noreferrer">{user.website}</a>
                                        </div>
                                    )}
                                    <div className="flex items-center gap-1 text-gray-500">
                                        <IoCalendarOutline />
                                        <span>Joined {user.joinDate}</span>
                                    </div>
                                </div>

                                {/* Stats */}
                                <div className="mt-6 flex gap-6">
                                    <div className="text-center">
                                        <div className="text-xl font-bold text-gray-900">{userStats.posts}</div>
                                        <div className="text-sm text-gray-500">Posts</div>
                                    </div>
                                    <div className="text-center">
                                        <div className="text-xl font-bold text-gray-900">{userStats.followers}</div>
                                        <div className="text-sm text-gray-500">Followers</div>
                                    </div>
                                    <div className="text-center">
                                        <div className="text-xl font-bold text-gray-900">{userStats.following}</div>
                                        <div className="text-sm text-gray-500">Following</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Content Tabs */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div className="bg-white rounded-xl shadow-sm p-4 mb-6">
                    <div className="flex gap-4">
                        <TabButton 
                            id="posts" 
                            label="Posts" 
                            icon={IoGridOutline}
                            count={userStats.posts}
                        />
                        <TabButton 
                            id="saved" 
                            label="Saved" 
                            icon={IoBookmarkOutline}
                            count={userStats.savedPosts}
                        />
                    </div>
                </div>

                {/* Posts Grid */}
                {activeTab === 'posts' && (
                    <div className="space-y-6 max-w-2xl mx-auto">
                        {posts.filter(post => post.user.id === user.id).map((post) => (
                            <Post key={post.id} post={post} />
                        ))}
                    </div>
                )}

                {/* Saved Posts Grid */}
                {activeTab === 'saved' && (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {savedPosts.length > 0 ? (
                            savedPosts.map((post) => (
                                <SavedPostCard key={post.id} post={post} />
                            ))
                        ) : (
                            <div className="col-span-full text-center py-12">
                                <div className="inline-block p-4 rounded-full bg-gray-100 mb-4">
                                    <IoBookmarkOutline className="w-8 h-8 text-gray-400" />
                                </div>
                                <h3 className="text-lg font-medium text-gray-900">No saved posts yet</h3>
                                <p className="mt-2 text-gray-500">
                                    When you save posts, they'll appear here.
                                </p>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </section>
    );
};

export default Profile;

