<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class Tag extends Model
{
    protected $fillable = [
        'tag',
        'usage_count',
        'last_used_at'
    ];

    protected $casts = [
        'last_used_at' => 'datetime',
    ];

    public function posts()
    {
        return $this->belongsToMany(Post::class);
    }

    /**
     * Get trending tags based on post count and recent activity
     *
     * @param int $limit Number of trending tags to return
     * @param int $days Number of days to consider for trending calculation
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getTrendingTags($limit = 10, $days = 7)
    {
        $dateThreshold = Carbon::now()->subDays($days);

        return self::select('tags.*', DB::raw('COUNT(post_tag.post_id) as posts_count'))
            ->join('post_tag', 'tags.id', '=', 'post_tag.tag_id')
            ->join('posts', 'post_tag.post_id', '=', 'posts.id')
            ->where('posts.created_at', '>=', $dateThreshold)
            ->groupBy('tags.id', 'tags.tag', 'tags.created_at', 'tags.updated_at')
            ->orderByDesc('posts_count')
            ->orderByDesc('tags.updated_at')
            ->limit($limit)
            ->get();
    }

    /**
     * Get all tags with their post counts
     *
     * @param int $limit Number of tags to return
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getTagsWithPostCount($limit = null)
    {
        $query = self::select('tags.*', DB::raw('COUNT(post_tag.post_id) as posts_count'))
            ->leftJoin('post_tag', 'tags.id', '=', 'post_tag.tag_id')
            ->groupBy('tags.id', 'tags.tag', 'tags.created_at', 'tags.updated_at')
            ->orderByDesc('posts_count');

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * Check if this tag is trending
     *
     * @param int $days Number of days to consider for trending
     * @param int $minPosts Minimum posts required to be considered trending
     * @return bool
     */
    public function isTrending($days = 7, $minPosts = 3)
    {
        $dateThreshold = Carbon::now()->subDays($days);

        $recentPostsCount = $this->posts()
            ->where('posts.created_at', '>=', $dateThreshold)
            ->count();

        return $recentPostsCount >= $minPosts;
    }

    /**
     * Increment usage count and update last used timestamp
     *
     * @return void
     */
    public function incrementUsage()
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => Carbon::now()]);
    }

    /**
     * Get trending tags with improved algorithm using usage count
     *
     * @param int $limit Number of trending tags to return
     * @param int $days Number of days to consider for trending calculation
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getTrendingTagsImproved($limit = 10, $days = 7)
    {
        $dateThreshold = Carbon::now()->subDays($days);

        return self::select('tags.*',
                DB::raw('COUNT(post_tag.post_id) as recent_posts_count'),
                DB::raw('(COUNT(post_tag.post_id) * 0.7 + tags.usage_count * 0.3) as trending_score'))
            ->join('post_tag', 'tags.id', '=', 'post_tag.tag_id')
            ->join('posts', 'post_tag.post_id', '=', 'posts.id')
            ->where('posts.created_at', '>=', $dateThreshold)
            ->groupBy('tags.id', 'tags.tag', 'tags.created_at', 'tags.updated_at', 'tags.usage_count', 'tags.last_used_at')
            ->orderByDesc('trending_score')
            ->orderByDesc('tags.last_used_at')
            ->limit($limit)
            ->get();
    }
}

