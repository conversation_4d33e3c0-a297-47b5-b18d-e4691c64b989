import api from './api';

/**
 * Tag Service - Handles all tag-related API calls
 */
class TagService {
    /**
     * Get all tags with their post counts
     * @returns {Promise} API response with tags data
     */
    async getAllTags() {
        try {
            const response = await api.get('/tags');
            return response.data;
        } catch (error) {
            console.error('Error fetching all tags:', error);
            throw error;
        }
    }

    /**
     * Get trending tags
     * @param {number} limit - Number of trending tags to fetch (default: 10)
     * @param {number} days - Number of days to consider for trending (default: 7)
     * @returns {Promise} API response with trending tags data
     */
    async getTrendingTags(limit = 10, days = 7) {
        try {
            const response = await api.get('/tags/trending', {
                params: { limit, days }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching trending tags:', error);
            throw error;
        }
    }

    /**
     * Search tags by name
     * @param {string} query - Search query
     * @param {number} limit - Number of results to return (default: 20)
     * @returns {Promise} API response with search results
     */
    async searchTags(query, limit = 20) {
        try {
            const response = await api.get('/tags/search', {
                params: { q: query, limit }
            });
            return response.data;
        } catch (error) {
            console.error('Error searching tags:', error);
            throw error;
        }
    }

    /**
     * Get a specific tag with its posts
     * @param {number} tagId - Tag ID
     * @returns {Promise} API response with tag and posts data
     */
    async getTagById(tagId) {
        try {
            const response = await api.get(`/tags/${tagId}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching tag by ID:', error);
            throw error;
        }
    }

    /**
     * Get posts by tag name
     * @param {string} tagName - Tag name
     * @returns {Promise} API response with posts data
     */
    async getPostsByTag(tagName) {
        try {
            // This would require additional backend endpoint or filtering
            const response = await api.get('/posts', {
                params: { tag: tagName }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching posts by tag:', error);
            throw error;
        }
    }
}

// Export a singleton instance
const tagService = new TagService();
export default tagService;
