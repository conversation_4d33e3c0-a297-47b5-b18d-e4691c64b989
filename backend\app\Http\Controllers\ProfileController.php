<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class ProfileController extends Controller
{
    public function updateProfile(Request $request)
    {
        $user = Auth::user();
        
        $validator = Validator::make($request->all(), [
            'nom' => 'sometimes|string|max:255',
            'prenom' => 'sometimes|string|max:255',
            'date_naissence' => 'sometimes|date|nullable',
            'sex' => 'sometimes|in:homme,femme',
            'profil' => 'sometimes|image|max:2048',
            'cover_image' => 'sometimes|image|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $data = $request->only(['nom', 'prenom', 'date_naissence', 'sex']);

        // Handle profile image upload
        if ($request->hasFile('profil')) {
            $profilePath = $request->file('profil')->store('profile_pic', 'public');
            $data['profil'] = Storage::url($profilePath);
        }

        // Handle cover image upload
        if ($request->hasFile('cover_image')) {
            $coverPath = $request->file('cover_image')->store('cover_images', 'public');
            $data['cover_image'] = Storage::url($coverPath);
        }

        $user->update($data);

        return response()->json([
            'message' => 'Profile updated successfully',
            'user' => $user
        ]);
    }

    public function getProfile()
    {
        $user = Auth::user();
        return response()->json($user);
    }
}