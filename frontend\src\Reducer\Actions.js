import { LOGIN, LOGOUT, TOGGLE_SAVE_POST } from "./ActionType";
import api from "../services/api";

export const loginSuccess = (user) => ({
  type: LOGIN,
  payload: user
});

export const logoutSuccess = () => ({
  type: LOGOUT
});

export const login = (userData) => {
  return (dispatch) => {
    // Directly dispatch the login success action with user data
    dispatch(loginSuccess(userData));
    return userData;
  };
};

export const register = (userData) => {
  return (dispatch) => {
    // Registration also logs the user in
    dispatch(loginSuccess(userData));
    return userData;
  };
};

export const logout = () => {
  return async (dispatch) => {
    try {
      // Call the logout endpoint if needed
      await api.post('/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Remove token and user from localStorage
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      
      // Dispatch logout action
      dispatch(logoutSuccess());
    }
  };
};

// User profile actions
export const updateUserProfile = (userData) => {
  return {
    type: 'UPDATE_USER_PROFILE',
    payload: userData
  };
};

// Save post actions
export const toggleSavePost = (postId) => {
  return {
    type: 'TOGGLE_SAVE_POST',
    payload: postId
  };
};
