import {
  LOGIN,
  LOGOUT,
  TOGGLE_SAVE_POST,
  FETCH_TRENDING_TAGS_REQUEST,
  FETCH_TRENDING_TAGS_SUCCESS,
  FETCH_TRENDING_TAGS_FAILURE,
  SET_TRENDING_TAGS
} from "./ActionType";
import api from "../services/api";
import tagService from "../services/tagService";

export const loginSuccess = (user) => ({
  type: LOGIN,
  payload: user
});

export const logoutSuccess = () => ({
  type: LOGOUT
});

export const login = (userData) => {
  return (dispatch) => {
    // Directly dispatch the login success action with user data
    dispatch(loginSuccess(userData));
    return userData;
  };
};

export const register = (userData) => {
  return (dispatch) => {
    // Registration also logs the user in
    dispatch(loginSuccess(userData));
    return userData;
  };
};

export const logout = () => {
  return async (dispatch) => {
    try {
      // Call the logout endpoint if needed
      await api.post('/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Remove token and user from localStorage
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      
      // Dispatch logout action
      dispatch(logoutSuccess());
    }
  };
};

// User profile actions
export const updateUserProfile = (userData) => {
  return {
    type: 'UPDATE_USER_PROFILE',
    payload: userData
  };
};

// Save post actions
export const toggleSavePost = (postId) => {
  return {
    type: 'TOGGLE_SAVE_POST',
    payload: postId
  };
};

// Trending tags actions
export const fetchTrendingTagsRequest = () => ({
  type: FETCH_TRENDING_TAGS_REQUEST
});

export const fetchTrendingTagsSuccess = (trendingTags) => ({
  type: FETCH_TRENDING_TAGS_SUCCESS,
  payload: trendingTags
});

export const fetchTrendingTagsFailure = (error) => ({
  type: FETCH_TRENDING_TAGS_FAILURE,
  payload: error
});

export const setTrendingTags = (trendingTags) => ({
  type: SET_TRENDING_TAGS,
  payload: trendingTags
});

// Async action to fetch trending tags
export const fetchTrendingTags = (limit = 10, days = 7) => {
  return async (dispatch) => {
    dispatch(fetchTrendingTagsRequest());

    try {
      const response = await tagService.getTrendingTags(limit, days);
      dispatch(fetchTrendingTagsSuccess(response.trending_tags));
      return response.trending_tags;
    } catch (error) {
      dispatch(fetchTrendingTagsFailure(error.message));
      throw error;
    }
  };
};
