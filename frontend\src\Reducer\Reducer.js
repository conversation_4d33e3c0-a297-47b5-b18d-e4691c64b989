import { LOGIN, LOGOUT, TOGGLE_SAVE_POST } from "./ActionType";

const initialState = {
    userConnected: localStorage.getItem('user') ? true : false,
    user: JSON.parse(localStorage.getItem('user')) || null,
    savedPosts: [],
    tandances: [
        { id: 1, title: 'Marrak<PERSON>', count: 200 },
        { id: 2, title: '<PERSON><PERSON>', count: 150 },
        { id: 3, title: 'Casablanca', count: 100 },
        { id: 4, title: 'Chefchaouen', count: 80 },
        { id: 5, title: '<PERSON><PERSON><PERSON><PERSON>', count: 75 },
    ],
    posts: [
        {
            id: 1,
            mapUrl: "https://maps.app.goo.gl/skRwgUxMY2QETh4K6",
            titre: "Sunset at Hassan II Mosque",
            content: "Captured this breathtaking sunset at the iconic Hassan II Mosque. The way the light reflects off the ocean and the intricate architecture creates a magical atmosphere. #Casablanca is truly stunning at golden hour! 🌅",
            user: {
                id: 1,
                name: "<PERSON><PERSON><PERSON><PERSON>",
                email: "<EMAIL>",
                profilePic: "https://media.istockphoto.com/id/1437816897/photo/business-woman-manager-or-human-resources-portrait-for-career-success-company-we-are-hiring.jpg?s=612x612&w=0&k=20&c=tyLvtzutRh22j9GqSGI33Z4HpIwv9vL_MZw_xOE19NQ=",
                username: "ibtissam"
            },
            likes: 245,
            place: "Casablanca",
            date: "2024-02-15",
            hashtags: ["Casablanca", "Morocco", "Travel", "Architecture", "Sunset"],
            media: [
                "https://images.unsplash.com/photo-1565689478170-6624de957899?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3",
                "https://images.unsplash.com/photo-1565689518111-8ee9978bbb35?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3",
            ],
            comments: [
                {
                    id: 1,
                    user: {
                        id: 2,
                        username: "sarah",
                        profilePic: "https://images.unsplash.com/photo-1494790108377-be9c29b29330",
                    },
                    content: "This is absolutely stunning! The colors are incredible 😍",
                    parentId: null,
                }
            ]
        },
        {
            id: 2,
            mapUrl: "https://maps.app.goo.gl/someMapUrl",
            titre: "Blue Pearl of Morocco",
            content: "Lost in the blue streets of Chefchaouen. Every corner is a photo opportunity! The local cats seem to know all the best spots for pictures 😺",
            user: {
                id: 2,
                name: "Sarah",
                email: "<EMAIL>",
                profilePic: "https://images.unsplash.com/photo-1494790108377-be9c29b29330",
                username: "sarah"
            },
            likes: 189,
            place: "Chefchaouen",
            date: "2024-02-14",
            hashtags: ["Chefchaouen", "Morocco", "Travel", "Photography", "BlueCity"],
            media: [
                "https://images.unsplash.com/photo-1553899017-4ff76981a06e?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3",
                "https://images.unsplash.com/photo-1553913861-c0fddf2619ee?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3",
            ]
        },
        {
            id: 3,
            mapUrl: "https://maps.app.goo.gl/anotherMapUrl",
            titre: "Marrakech Magic",
            content: "Exploring the vibrant souks of Marrakech! The spice markets are a feast for all senses. Found some amazing traditional crafts and had the best mint tea ever! ✨",
            user: {
                id: 3,
                name: "Ahmed",
                email: "<EMAIL>",
                profilePic: "https://images.unsplash.com/photo-1566492031773-4f4e44671857",
                username: "ahmed"
            },
            likes: 312,
            place: "Marrakech",
            date: "2024-02-13",
            hashtags: ["Marrakech", "Morocco", "Travel", "Culture", "Souk"],
            media: [
                "https://images.unsplash.com/photo-1547644769-9c8794a5f2a8?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3",
                "https://images.unsplash.com/photo-1539020140153-e479b8c6007a?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3",
            ]
        },
        {
            id: 4,
            mapUrl: "https://maps.app.goo.gl/yetAnotherMapUrl",
            titre: "Essaouira Waves",
            content: "Perfect day for kitesurfing in Essaouira! The wind and waves were just right. Met some amazing fellow surfers from all around the world 🏄‍♂️",
            user: {
                id: 4,
                name: "Karim",
                email: "<EMAIL>",
                profilePic: "https://images.unsplash.com/photo-1566492031773-4f4e44671857",
                username: "karim"
            },
            likes: 167,
            place: "Essaouira",
            date: "2024-02-12",
            hashtags: ["Essaouira", "Morocco", "Sports", "Surfing", "Beach"],
            media: [
                "https://images.unsplash.com/photo-1596627116790-af6f46dddbda?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3",
                "https://images.unsplash.com/photo-1596627116790-af6f46dddbda?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3",
            ]
        },
        {
            id: 5,
            mapUrl: "https://maps.app.goo.gl/oneMoreMapUrl",
            titre: "Rabat's Royal Touch",
            content: "Visiting the Kasbah of the Udayas in Rabat. The mix of traditional architecture and modern city life is fascinating. Don't miss the beautiful Andalusian Gardens! 🌸",
            user: {
                id: 5,
                name: "Leila",
                email: "<EMAIL>",
                profilePic: "https://images.unsplash.com/photo-1494790108377-be9c29b29330",
                username: "leila"
            },
            likes: 203,
            place: "Rabat",
            date: "2024-02-11",
            hashtags: ["Rabat", "Morocco", "History", "Architecture", "Gardens"],
            media: [
                "https://images.unsplash.com/photo-1553899017-4ff76981a06e?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3",
                "https://images.unsplash.com/photo-1553913861-c0fddf2619ee?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3",
            ]
        },
        {
            id: 6,
            mapUrl: "https://maps.app.goo.gl/lastMapUrl",
            titre: "Tangier Tales",
            content: "Watching ships come and go at the Tangier port. This city where Europe meets Africa has such a unique energy! Found this amazing café with views of both continents 🚢",
            user: {
                id: 1,
                name: "Ibtissam",
                email: "<EMAIL>",
                profilePic: "https://media.istockphoto.com/id/1437816897/photo/business-woman-manager-or-human-resources-portrait-for-career-success-company-we-are-hiring.jpg?s=612x612&w=0&k=20&c=tyLvtzutRh22j9GqSGI33Z4HpIwv9vL_MZw_xOE19NQ=",
                username: "ibtissam"
            },
            likes: 178,
            place: "Tangier",
            date: "2024-02-10",
            hashtags: ["Tangier", "Morocco", "Travel", "Port", "Café"],
            media: [
                "https://images.unsplash.com/photo-1553899017-4ff76981a06e?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3",
                "https://images.unsplash.com/photo-1553913861-c0fddf2619ee?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3",
            ]
        }
    ]
};

const reducer = (state = initialState, action) => {
    switch (action.type) {
        case LOGIN:
            return {
                ...state,
                userConnected: true,
                user: action.payload
            };
        case LOGOUT:
            return {
                ...state,
                userConnected: false,
                user: null
            };
        case 'UPDATE_USER_PROFILE':
            return {
                ...state,
                user: {
                    ...state.user,
                    ...action.payload
                }
            };
        case TOGGLE_SAVE_POST:
            const postId = action.payload;
            const savedPosts = state.savedPosts || [];
            
            if (savedPosts.includes(postId)) {
                return {
                    ...state,
                    savedPosts: savedPosts.filter(id => id !== postId)
                };
            } else {
                return {
                    ...state,
                    savedPosts: [...savedPosts, postId]
                };
            }
        default:
            return state;
    }
};

export default reducer;
